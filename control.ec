!----------------------------------------------------------------------------------------------------
!                    通用设置
!----------------------------------------------------------------------------------------------------

$project
  project_rootname="4.3w" !网格文件的名字
  project_casename="C1"
$end

$control_ec
  NUM_THREADS=1         ! Threads for OpenMP
  IF_Debug=0            ! 1 Debug mode
  IF_Innerflow = 0! 内流模式 (入口边界条件与叶轮机模式类似)
$end


$freestream_meanflow
  Iflag_nondimensional_kind=1 !xDimensional=2 xNoneDimensionalSoundSpeed=1 xNoneDimensionalFreeSpeed=3
  Iflag_init=0
  Ma=3.5
  Re=6.42e7
  AOA=0.d0       ! Angle of attack
  AOS=0.d0       ! Angle of Slide
  IFlag_freestream_condition = 1 !自由来流的给法，1 雷诺数温度， 2：温度，密度，压力，3：根据高度
  Pressure_inf = 63450.87 !自由来流压力，IFlag_freestream_condition=2有效
  Rho_inf  =  0.7369 !自由来流密度，IFlag_freestream_condition=2有效
  UVW_inf =-1   !-1 means use Mach number, otherwise use the velocity
  T_inf=92.46        ! Reference temperature  (in viscous coefficient)
  H_inf=0
  Twall=-1           ! Wall temperature (in K degree) (<0 adabitic)
  Kt_inf=1.d-5          ! initial of Kt for SST model
  Wt_inf=0.01d0         ! Initial of Wt for SST model
$end



$grids_options
  UNDIM_L = 0.001 !网格参考尺度，计算雷诺数使用，实际长度/UNDIM_L=网格文件的长度
  Cood_Y_UP=1   !       默认Y轴垂直向上
  Ref_S=1.d0             ! Ref. area
  Ref_L=1.d0             ! Ref. length
  Centroid= 0.0 ,0.0 ,0.0
  Mesh_File_Format=3    ! 0 unformatted, 1 formatted， 2 gridgen generic unformatted, 3 cgns to plot3d
$end

$physical_models_equations
  Iflag_turbulence_model=13
!Turbulence_TRANS_KW=31, Turbulence_TRANS_SA=32,Turbulence_TRANS_SST=33,&
!Turbulence_TRANS_MSA=34, Turbulence_TRANS_KW_L2W=35,&
!Turbulence_trans_kwg = 6,Turbulence_trans_greq=7,&
!Turbulence_trans_SST_trans_crit=8,Turbulence_trans_ktklw=9,Turbulence_trans_gamma=10,&
!Turbulence_trans_kwg_roughness = 11,Turbulence_trans_kwg_Liu = 13
  If_source = 0      !计算槽道流的时候开启
  Re_tau = 180     !计算槽道流的时候设置壁面摩擦雷诺数
  If_viscous=2  ! 0 无粘； 1 有粘； 2, 薄层N-S方程，算高超复杂外形不容易发散, 3,全ns方程
  If_LES =0  !0:无大涡模拟，1：大涡模拟
  LES_filter_ratio=2 !%% Filter width ratio. Try range 1:2.
  LES_SGS_model=0 !%% 0: Smagorinsky, 1: WALE,
  LES_CS=0.1
  LES_WALE_CW=0.544
  LES_Pr_sGs=0.5 ![0.3-0.9]
  LES_CI=0.0
$end
!----------------------------------------------------------------------------------------------------
!                    空间离散设置
!----------------------------------------------------------------------------------------------------

$inviscid_scheme
  Iflag_Scheme= 11    ! 0 UD1;  1 NND2 ; 2 UD3 ; 3 MUSCL2U ;  4 MUSCL2C; 5 MUSCL3; 6 OMUSCL2; 7 WENO5 ; 8 UD5; 9 WENO7  ;10 WENO_SYMBO7;Scheme_MUSCL_limiter=11
  Iflag_MUSCL2U_limiter = 2!限制器选项，注意只有迎风格式使用限制器，# 1:   'VanAlbada', #2:    'minmod2', #3:    'van_leer_ave',4:supperbe 5:vanleer
  Iflag_Flux=9      ! 7：AUSMPW+M格式；8：SLAU格式；4：Roe格式;9:RoeM;10 RoeAPC
  IFlag_Reconstruction=0   !  0 Original, 1 Conservative, 2 Characteristic
  Bound_Scheme= 11         ! Boundary scheme (Default: MUSCL2C，-1:不适用边界格式)
  IFlag_entropy_fix=1 !0：无熵修正，1:熵修正muller
  Entropy_fix_O = 0.45  !指数 ,熵修正系数，需给定两项数值：Entropy_Fix_D, Entropy_Fix_O,EntropyFixO代表熵修正指数，一般取为0.2-0.6
  Entropy_fix_D = 0.3 !系数,EntropyFixD代表熵修正系数，一般取为0.1（低M数）-0.4（高M数）
$end


!----------------------------------------------------------------------------------------------------
!                    时间离散设置
!----------------------------------------------------------------------------------------------------

$time_accurate
  max_iteration=1000  !最大迭代步数
  t_end=1e10     ! End time
  Iflag_input_time_ND=0 !!1:输入的时间为无量纲，0:输入的时间为有量纲
  Iflag_local_dt=1   ! 0 全局步长；  1 局部时间步长
  dt_global=1e-3     ! Global time step
  CFL=5                  ! CFL number
  dtmax=10.d0       !Limit of maximum time step
  dtmin=1.d-9       !Limit of minimum time step
  Time_Method=0     ! Time_Euler1=1,Time_RK3=3,Time_LU_SGS=0, Time_dual_LU_SGS=-1
$end

$LU_SGS
  w_LU=1.04d0         ! factor in LU-SGS
  Step_Inner_Limit=20   ! Inner time advance limit (Dual-time)
  Res_Inner_Limit=1.d-3 ! Inner Resdial limit (Dual-time，目前bo被废止了，考虑相对残差下降一个量级作为标准)
$end

$multigrid
  Num_Mesh=1            ! Number of mesh (1 single-grid), 2,3 multi-grid
  Pre_Step_Mesh=0 0 0   ! Pre step for multi-grid
$end
!----------------------------------------------------------------------------------------------------
!                    输出设置
!----------------------------------------------------------------------------------------------------

$output_control
  AverageOut_Interval = 0 !时均统计输出的间隔
  Time_begin_average = 0 !!开始时均统计的时刻
  Kstep_save=20000  ! Save data per xxx steps，存储的续算文件
  Kstep_show=1          ! show per xxx steps
  Kstep_smooth=-1       ! Smoothing flow per xxx steps (<0 donot smooth)
  Kstep_init_smooth=0   ! Smoothing flow at initial time
  Plot_Interval=1000 !输出tecplot体流场的步数
  Plot_File_type = 1 !!0:structured file 1: union unstructured file
  Diag_Interval=0 !输出表面流场的步数
  Kstep_average=0 !计算平均流场的间隔
  Iflag_savefile = 0 ! 0 保存到flow3d.dat, 1 保存到flow3d-xxxxxxx.dat
  K_slice = 0 !保存第K截面
  Pdebug=1, 1, 1, 1  !n,i,j,k
  Plot_verbosity = 1 ! 1：详细输出 2:普通输出
$end

$perfect_gas_model
  gas_gamma=1.4d0    !
  PrL=0.7d0          ! Linear Prandtl number
  PrT=0.9d0          ! Turbulent Prandtl number

$end

$robust_limits
  IFLAG_LIMIT_FLOW=0         ! 限制流场（密度、速度、压力）
  MUT_MAX=-1.d0          ! Limit for vt/vs (<0 no limit)
  If_Residual_smoothing=0   ! 0 Do not need smoothing
  Ldmin=1.d-6
  Ldmax=1000.d0
  Lpmin=1.d-6
  Lpmax=1000.d0
  Lumax=1000.d0
  LSAmax=1000.d0
  If_dtime_mesh=1   ! decrease time step when grid quality is not good
$end

$turbulence_sa_para
  CP1_NSA=0.2d0   ! for New SA
  CP2_NSA=100.d0
  msa_const_h1=0.71
  msa_const_h2=0.6
$end

$turbulence_l2w_para
   L2W_Length_kind =2 ! 0：kw模型， 1:l2w, 2:自适应l2w
   L2W_Clim_kind = 1 ! 0: clim=0.01, 1: clim = Yin_2015, 2: clim = Yin_2016
   L2W_CDES = 0.12
$end

$lowSpeedPrecondition
  Iflag_lowSpeedPrecondition = 0
  lowSpeedCutoffCoe = 0.1
$end
!----for Turbomachinary solver------------
$turboMachinary
  IF_TurboMachinary=0    ! 启用叶轮机计算模式
  Ref_medium_usrdef=0    ! 启用自定义介质 （为默认空气）
  IF_Scheme_Positivity=1     ! 检查插值过程中压力、密度是否非负，否则使用1阶迎风；
  Turbo_P0= 101330.d0    ! 总压 （默认为1个大气压）
  Turbo_T0= 288.15d0     ! 总温 （默认288.15K)
  Turbo_L0= 1.d0         ! 参考长度 （默认为1m)
  Turbo_w=0.d0           ! 转速  ( 转/秒 ， 默认0)
  Turbo_Periodic_seta=0.d0   ! 周期方向计算域，角
$end






