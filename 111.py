import sys
import os
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                             QFileDialog, QGroupBox, QFormLayout, QMessageBox,
                             QSpinBox, QTabWidget, QScrollArea, QCheckBox,
                             QComboBox, QDoubleSpinBox, QGridLayout)
from PyQt5.QtCore import Qt, QProcess

class CFDCalculator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("ATT-Solver")
        self.setMinimumSize(2800, 1400)
        
        # 初始化变量
        self.process = None
        self.param_widgets = {}  # 存储所有参数控件的字典
        
        # 设置中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建参数选项卡
        self.tab_widget = QTabWidget()
        self.create_parameter_tabs()
        main_layout.addWidget(self.tab_widget)
        
        # MPI进程数控制部分
        exe_group = QGroupBox("执行配置")
        exe_layout = QHBoxLayout()
        
        # 添加MPI进程数选择
        exe_layout.addWidget(QLabel("MPI进程数:"))
        self.process_count = QSpinBox()
        self.process_count.setMinimum(1)
        self.process_count.setMaximum(128)  # 设置最大进程数，可根据实际需求调整
        self.process_count.setValue(10)     # 默认使用10个进程
        exe_layout.addWidget(self.process_count)
        
        # 可以添加更多的执行选项
        exe_layout.addStretch(1)
        
        exe_group.setLayout(exe_layout)
        main_layout.addWidget(exe_group)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        self.start_button = QPushButton("启动计算")
        self.start_button.clicked.connect(self.start_calculation)
        
        self.stop_button = QPushButton("停止计算")
        self.stop_button.clicked.connect(self.stop_calculation)
        self.stop_button.setEnabled(False)
        
        self.save_params_button = QPushButton("保存参数")
        self.save_params_button.clicked.connect(self.save_parameters)
        
        self.load_params_button = QPushButton("加载参数")
        self.load_params_button.clicked.connect(self.load_parameters)
        
        control_layout.addWidget(self.save_params_button)
        control_layout.addWidget(self.load_params_button)
        control_layout.addStretch(1)
        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.stop_button)
        
        main_layout.addLayout(control_layout)
        
        # 状态输出区域
        status_group = QGroupBox("计算状态")
        status_layout = QVBoxLayout()
        self.status_label = QLabel("就绪")
        self.status_label.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        status_scroll = QScrollArea()
        status_scroll.setWidget(self.status_label)
        status_scroll.setWidgetResizable(True)
        status_scroll.setMinimumHeight(100)
        status_layout.addWidget(status_scroll)
        status_group.setLayout(status_layout)
        
        main_layout.addWidget(status_group)
    
    def create_parameter_tabs(self):
        """创建参数选项卡"""
        # 定义所有参数分组
        parameter_groups = [
            "project", 
            "control_ec", 
            "freestream_meanflow", 
            "grids_options", 
            "physical_models_equations", 
            "inviscid_scheme", 
            "time_accurate", 
            "LU_SGS", 
            "multigrid", 
            "output_control", 
            "perfect_gas_model", 
            "robust_limits", 
            "turbulence_l2w_para", 
            "turbulence_sa_para", 
            "lowSpeedPrecondition", 
            "turboMachinary"
        ]
        
        # 为每个分组创建选项卡
        for group in parameter_groups:
            tab = QWidget()
            scroll = QScrollArea()
            scroll.setWidget(tab)
            scroll.setWidgetResizable(True)
            
            # 使用网格布局，更灵活地排列参数
            layout = QGridLayout(tab)
            
            # 根据不同分组添加不同的参数
            self.add_parameters_for_group(group, layout)
            
            # 将选项卡添加到选项卡控件中
            self.tab_widget.addTab(scroll, group.replace("_", " ").title())
    
    def add_parameters_for_group(self, group, layout):
        """为每个参数组添加相应的参数控件"""
        row = 0
        
        # 根据不同的组添加不同的参数
    def add_parameters_for_group(self, group, layout):
        """为每个参数组添加相应的参数控件"""
        row = 0
        
        # 根据不同的组添加不同的参数
        if group == "project":
            # 项目基本信息参数
            self.add_text_parameter(layout, row, group, "project_rootname", "网格文件名称", "CFD_Simulation")
            row += 1
            self.add_text_parameter(layout, row, group, "project_casename", "保存文件名称", "")
            # row += 1
            # self.add_text_parameter(layout, row, group, "author", "作者", "")
            
        elif group == "control_ec":
            # 控制参数
            # self.add_combo_parameter(layout, row, group, "solver_type", "求解器类型", 
            #                         ["euler", "ns", "rans"], "rans")
            # row += 1
            self.add_int_parameter(layout, row, group, "NUM_THREADS", "OpenMP核心", 1, 1, 1000000)
            row += 1
            self.add_int_parameter(layout, row, group, "IF_Debug", "DeBug模式", 0, 0, 1)
            row += 1
            self.add_int_parameter(layout, row, group, "IF_Innerflow", "内流模式", 0, 0, 1)
            
        elif group == "freestream_meanflow":
            # 自由流参数
            self.add_int_parameter(layout, row, group, "Iflag_nondimensional_kind", "无量纲模式", 1, 1, 3)
            row += 1
            self.add_int_parameter(layout, row, group, "Iflag_init", "是否续算", 0, 0, 1)
            row += 1
            self.add_double_parameter(layout, row, group, "Ma", "马赫数", 0.5, 0.0, 10.0)
            row += 1
            self.add_double_parameter(layout, row, group, "Re", "雷诺数", 1e6, 0.0, 1e9)
            row += 1
            self.add_double_parameter(layout, row, group, "AOA", "攻角", 0.0, -90.0, 90.0)
            row += 1
            self.add_double_parameter(layout, row, group, "sideslip", "侧滑角", 0.0, -90.0, 90.0)
            row += 1
            self.add_int_parameter(layout, row, group, "IFlag_freestream_condition", "自由来流模式", 1, 1, 3)
            row += 1
            self.add_double_parameter(layout, row, group, "Pressure_inf", "压力(Pa)", 101325.0, 0.0, 1e7)
            row += 1
            self.add_double_parameter(layout, row, group, "Rho_inf", "密度(kg/m3)", 1.0, 0.0, 5000.0)
            row += 1
            self.add_double_parameter(layout, row, group, "UVW_inf", "速度(m/s)", -1, -10.0, 10000.0)
            row += 1
            self.add_double_parameter(layout, row, group, "T_inf", "温度(K)", 288.15, 0.0, 5000.0)
            row += 1
            self.add_double_parameter(layout, row, group, "T_wall", "壁面温度(K)", 288.15, -10.0, 5000.0)
            row += 1
            self.add_double_parameter(layout, row, group, "Kt_inf", "SST模型初始值Kt", 1e-5, 1e-8, 1e-3)
            row += 1
            self.add_double_parameter(layout, row, group, "Wt_inf", "SST模型初始值Wt", 0.01, 0.0001, 1.0)
            
        elif group == "grids_options":
            # 网格选项
            # self.add_combo_parameter(layout, row, group, "grid_type", "网格类型", 
            #                         ["structured", "unstructured", "hybrid"], "structured")
            self.add_double_parameter(layout, row, group, "UNDIM_L", "网格参考尺度", 0.001, 0.001, 1e6)
            row += 1
            self.add_int_parameter(layout, row, group, "Cood_Y_UP", "法向方向", 1, 0, 2)
            row += 1
            self.add_double_parameter(layout, row, group, "Ref_S", "参考面积", 1.0, 1e-8, 1e8)
            row += 1
            self.add_double_parameter(layout, row, group, "Ref_L", "参考长度", 1.0, 1e-8, 1e8)
            row += 1
            self.add_text_parameter(layout, row, group, "Centroid", "中心", "0.0, 0.0, 0.0")
            row += 1
            self.add_int_parameter(layout, row, group, "Mesh_File_Format", "网格类型", 3, 0, 3)
            
        elif group == "physical_models_equations":
            # 物理模型和方程
            self.add_combo_parameter(layout, row, group, "Iflag_turbulence_model", "方程类型", 
                                    ["Turbulence_TRANS_KW", "Turbulence_TRANS_SA", "Turbulence_TRANS_SST",
                                     "Turbulence_TRANS_MSA","Turbulence_TRANS_KW_L2W","Turbulence_trans_kwg",
                                     "Turbulence_trans_greq","Turbulence_trans_SST_trans_crit",
                                     "Turbulence_trans_ktklw","Turbulence_trans_gamma",
                                     "Turbulence_trans_kwg_roughness","Turbulence_trans_kwg_Liu"], "Turbulence_TRANS_SST")
            row += 1
            # self.add_combo_parameter(layout, row, group, "gas_model", "气体模型", 
            #                         ["perfect_gas", "real_gas"], "perfect_gas")
            self.add_bool_parameter(layout, row, group, "If_source", "源项", False)
            row += 1
            self.add_double_parameter(layout, row, group, "Re_tau", "壁面摩擦雷诺数", 180, 0.0, 1e9)
            row += 1
            self.add_combo_parameter(layout, row, group, "If_viscous", "粘性项", ["无粘","有粘","薄层N-S方程","ns方程"],"ns方程")
            row += 1
            self.add_bool_parameter(layout, row, group, "If_LES", "大涡模拟", False)
            row += 1
            self.add_int_parameter(layout, row, group, "LES_filter_ratio", "filter ratio", 2, 1, 2)
            row += 1
            self.add_int_parameter(layout, row, group, "LES_SGS_model", "SGS模型", 0, 0, 1)
            row += 1
            self.add_double_parameter(layout, row, group, "LES_CS", "LES CS", 0.1, 0.001, 10.0)
            row += 1
            self.add_double_parameter(layout, row, group, "LES_WALE_CW", "LES WALE CW", 0.544, 1e-3, 1.0)
            row += 1
            self.add_double_parameter(layout, row, group, "LES_WALE_CK", "LES Pr sGs", 0.5, 0.3, 0.9)
            row += 1
            # self.add_bool_parameter(layout, row, group, "heat_transfer", "考虑热传导", True)
            self.add_double_parameter(layout, row, group, "LES_CI", "LES CI", 0.0, 0.001, 1.0)
            
        elif group == "inviscid_scheme":
            # 无粘格式
            self.add_combo_parameter(layout, row, group, "Iflag_Scheme", "离散格式", 
                                    ["UD1", "NND2", "UD3", "MUSCL2U","MUSCL2C","MUSCL3",
                                     "OMUSCL2","WENO5","UD5","WENO7","WENO_SYMBO7","Scheme_MUSCL_limiter"], "Scheme_MUSCL_limiter")
            row += 1
            self.add_combo_parameter(layout, row, group, "Iflag_MUSCL2U_limiter", "限制器类型", 
                                    ["VanAlbada", "minmod2", "van_leer_ave", "vanleer"], "minmod2")
            row += 1
            self.add_combo_parameter(layout, row, group, "Iflag_Flux", "通量格式", 
                                    ["AUSMPW+M", "SLAU", "Roe", "RoeM","RoeAPC"], "RoeM")
            row += 1
            self.add_int_parameter(layout, row, group, "IFlag_Reconstructio", "重构", 0, 1, 2)
            row += 1
            self.add_int_parameter(layout, row, group, "Bound_Scheme", "边界格式", 11, -1, 11)
            row += 1
            self.add_bool_parameter(layout, row, group, "IFlag_entropy_fix", "熵修正", False)
            row += 1
            self.add_double_parameter(layout, row, group, "Entropy_fix_O", "熵修正系数O", 0.45, 0.2, 0.6)
            row += 1
            self.add_double_parameter(layout, row, group, "Entropy_fix_D", "熵修正系数D", 0.3, 0.1, 0.4)
            
        elif group == "time_accurate":
            # 时间推进
            self.add_int_parameter(layout, row, group, "max_iteration", "最大迭代步数", 10000, 1, 100000000)
            row += 1
            self.add_double_parameter(layout, row, group, "t_end", "结束时间", 1e10, 1e-10, 1e15)
            row += 1
            self.add_bool_parameter(layout, row, group, "Iflag_input_time_ND", "无量纲时间", False)
            row += 1
            self.add_bool_parameter(layout, row, group, "Iflag_local_dt", "局部时间步长", False)
            row += 1
            self.add_double_parameter(layout, row, group, "dt_global", "时间步长", 0.001, 1e-6, 1.0)
            row += 1
            self.add_double_parameter(layout, row, group, "CFL", "CFL数", 5.0, 0.1, 10.0)
            row += 1
            self.add_double_parameter(layout, row, group, "dtmax", "最大时间步长", 10.0, 1.0, 100.0)
            row += 1
            self.add_double_parameter(layout, row, group, "dtmin", "最小时间步长", 1e-9, 1e-10, 1.0)
            row += 1
            self.add_combo_parameter(layout, row, group, "time_stepping", "时间推进方式", 
                                    ["steady", "unsteady"], "steady")
            row += 1
            self.add_combo_parameter(layout, row, group, "Time_Method", "时间离散格式", 
                                    ["Time_Euler", "Time_RK3","Time_LU_SGS","Time_dual_LU_SGS"], "Time_LU_SGS")
            
        elif group == "LU_SGS":
            # LU-SGS参数
            self.add_double_parameter(layout, row, group, "w_LU", "LU SGS因子", 1.04, 1.0, 1.5)
            row += 1
            self.add_int_parameter(layout, row, group, "Step_Inner_Limit", "时间推进限制", 20, 1, 100)
            row += 1
            self.add_double_parameter(layout, row, group, "Res_Inner_Limit", "残差限制", 1e-3, 1e-6, 1e-3)
            
        elif group == "multigrid":
            # 多重网格参数
            self.add_int_parameter(layout, row, group, "Num_Mesh", "多重网格", 1, 1, 3)
            row += 1
            self.add_int_parameter(layout, row, group, "Pre_Step_Mesh", "光滑步数", 0, 0, 10)

            
        elif group == "output_control":
            # 输出控制
            self.add_int_parameter(layout, row, group, "AverageOut_Interval", "时均统计输出的间隔", 0, 0, 100)
            row += 1
            self.add_double_parameter(layout, row, group, "Time_begin_average", "开始时均统计的时刻", 0.0, 0.0, 100.0)
            row += 1
            self.add_int_parameter(layout, row, group, "Kstep_save", "存储续算文件", 0, 0, 100000)
            row += 1
            self.add_int_parameter(layout, row, group, "Kstep_show", "输出残差间隔", 1, 1, 100)
            row += 1
            self.add_int_parameter(layout, row, group, "Kstep_smooth", "顺滑间隔", -1, -1, 100)
            row += 1
            self.add_bool_parameter(layout, row, group, "Kstep_init_smooth", "初始化顺滑", False)
            row += 1
            self.add_int_parameter(layout, row, group, "Plot_Interval", "输出体流场间隔", 1000, 0, 100000)
            row += 1
            self.add_int_parameter(layout, row, group, "Plot_File_type", "流场文件格式", 1, 0, 1)
            row += 1
            self.add_int_parameter(layout, row, group, "Diag_Interval", "输出面流场间隔", 0, 0, 100000)
            row += 1
            self.add_int_parameter(layout, row, group, "Kstep_average", "计算平均流场间隔", 0, 0, 10000)
            row += 1
            self.add_int_parameter(layout, row, group, "Iflag_savefile", "续算文件格式", 0, 0, 1)
            row += 1
            self.add_int_parameter(layout, row, group, "K_slice", "保存截面", 0, 0, 100)
            row += 1
            self.add_text_parameter(layout, row, group, "Pdebug", "保存节点", "1, 1, 1, 1")
            row += 1
            self.add_int_parameter(layout, row, group, "Plot_verbosity", "输出模式", 1, 1, 2)
            
        elif group == "perfect_gas_model":
            # 完全气体模型参数
            self.add_double_parameter(layout, row, group, "gas_gamma", "比热比", 1.4, 1.0, 2.0)
            row += 1
            self.add_double_parameter(layout, row, group, "PrL", "线性普朗特数", 0.7, 0.1, 10.0)
            row += 1
            self.add_double_parameter(layout, row, group, "PrT", "湍流普朗特数", 0.9, 0.1, 10.0)
            
        elif group == "robust_limits":
            # 稳健性限制
            self.add_bool_parameter(layout, row, group, "IFLAG_LIMIT_FLOW", "使用稳健性限制", False)
            row += 1
            self.add_double_parameter(layout, row, group, "MUT_MAX", "最大涡粘系数", -1.0, 1e-10, 1e10)
            row += 1
            self.add_bool_parameter(layout, row, group, "If_Residual_smoothing", "残差顺滑", False)
            row += 1
            self.add_double_parameter(layout, row, group, "min_density", "最小密度", 1e-6, 1e-10, 1.0)
            row += 1
            self.add_double_parameter(layout, row, group, "max_density", "最大密度", 1e6, 1.0, 1e10)
            row += 1
            self.add_double_parameter(layout, row, group, "max_pressure", "最大压力", 1e10, 1.0, 1e15)
            row += 1
            self.add_double_parameter(layout, row, group, "min_pressure", "最小压力", 1e-6, 1e-10, 1.0)
            row += 1
            self.add_double_parameter(layout, row, group, "LSAmax", "SA模型", 1000.0, 0.1, 100.0)
            row += 1
            self.add_double_parameter(layout, row, group, "If_dtime_mesh", "减小时间步长", 1.0, 0.001, 10000.0)

        elif group == "turbulence_sa_para":
            # SA湍流模型参数
            self.add_double_parameter(layout, row, group, "CP1_NSA", "CP1系数", 0.2, 0.1, 1.0)
            row += 1
            self.add_double_parameter(layout, row, group, "CP2_NSA", "CP2系数", 100.0, 0.1, 10000.0)
            row += 1
            self.add_double_parameter(layout, row, group, "msa_const_h1", "h1系数", 0.71, 0.1, 10.0)
            row += 1
            self.add_double_parameter(layout, row, group, "msa_const_h2", "h2系数", 0.6, 0.1, 10.0)

        elif group == "turbulence_l2w_para":
            # L2W湍流模型参数
            self.add_int_parameter(layout, row, group, "L2W_Length_kind", "长度类型", 2, 0, 2)
            row += 1
            self.add_int_parameter(layout, row, group, "L2W_Clim_kind", "Clim类型", 0, 0, 2)
            row += 1
            self.add_double_parameter(layout, row, group, "L2W_CDES", "CDES系数", 0.12, 0.1, 10.0)
    
            
        elif group == "lowSpeedPrecondition":
            # 低速预处理
            self.add_bool_parameter(layout, row, group, "Iflag_lowSpeedPrecondition", "使用低速预处理", False)
            row += 1
            self.add_double_parameter(layout, row, group, "lowSpeedCutoffCoe", "预处理截断马赫数", 0.1, 0.001, 1.0)
            
        elif group == "turboMachinary":
            # 涡轮机械参数
            self.add_bool_parameter(layout, row, group, "IF_TurboMachinary", "是否为叶轮机计算模式", False)
            row += 1
            self.add_bool_parameter(layout, row, group, "Ref_medium_usrdef", "启用自定义介质", False)
            row += 1
            self.add_bool_parameter(layout, row, group, "IF_Scheme_Positivity", "检查压力密度是否为负", True)
            row += 1
            self.add_double_parameter(layout, row, group, "Turbo_P0", "总压", 101330.0, 0.0, 1e7)
            row += 1
            self.add_double_parameter(layout, row, group, "Turbo_T0", "总温", 288.15, 0.0, 5000.0)
            row += 1
            self.add_double_parameter(layout, row, group, "Turbo_L0", "参考长度", 1.0, 1e-6, 1e7)
            row += 1
            self.add_double_parameter(layout, row, group, "Turbo_w", "转速(rps)", 0.0, 0.0, 1e7)
            row += 1
            self.add_double_parameter(layout, row, group, "Turbo_Periodic_seta", "周期方向计算域(度)", 0.0, 0.0, 360.0)
            
    def add_text_parameter(self, layout, row, group, name, label_text, default_value):
        """添加文本参数"""
        label = QLabel(label_text)
        widget = QLineEdit(default_value)
        
        layout.addWidget(label, row, 0)
        layout.addWidget(widget, row, 1)
        
        # 存储控件引用
        self.param_widgets[f"{group}.{name}"] = widget
    
    def add_int_parameter(self, layout, row, group, name, label_text, default_value, min_value=0, max_value=1000000):
        """添加整数参数"""
        label = QLabel(label_text)
        widget = QSpinBox()
        widget.setMinimum(min_value)
        widget.setMaximum(max_value)
        widget.setValue(default_value)
        
        layout.addWidget(label, row, 0)
        layout.addWidget(widget, row, 1)
        
        # 存储控件引用
        self.param_widgets[f"{group}.{name}"] = widget
    
    def add_double_parameter(self, layout, row, group, name, label_text, default_value, min_value=0.0, max_value=1000000.0):
        """添加浮点数参数"""
        label = QLabel(label_text)
        widget = QDoubleSpinBox()
        widget.setMinimum(min_value)
        widget.setMaximum(max_value)
        widget.setValue(default_value)
        widget.setDecimals(6)  # 设置小数位数
        widget.setSingleStep(0.1)  # 设置步长
        
        layout.addWidget(label, row, 0)
        layout.addWidget(widget, row, 1)
        
        # 存储控件引用
        self.param_widgets[f"{group}.{name}"] = widget
    
    def add_bool_parameter(self, layout, row, group, name, label_text, default_value):
        """添加布尔参数"""
        label = QLabel(label_text)
        widget = QCheckBox()
        widget.setChecked(default_value)
        
        layout.addWidget(label, row, 0)
        layout.addWidget(widget, row, 1)
        
        # 存储控件引用
        self.param_widgets[f"{group}.{name}"] = widget
    
    def add_combo_parameter(self, layout, row, group, name, label_text, options, default_value):
        """添加下拉选择参数"""
        label = QLabel(label_text)
        widget = QComboBox()
        widget.addItems(options)
        
        # 设置默认值
        default_index = options.index(default_value) if default_value in options else 0
        widget.setCurrentIndex(default_index)
        
        layout.addWidget(label, row, 0)
        layout.addWidget(widget, row, 1)
        
        # 存储控件引用
        self.param_widgets[f"{group}.{name}"] = widget
    
    def get_parameter_values(self):
        """获取所有参数的值"""
        params = {}
        
        for key, widget in self.param_widgets.items():
            group, name = key.split('.')
            
            if group not in params:
                params[group] = {}
                
            # 根据控件类型获取值
            if isinstance(widget, QLineEdit):
                params[group][name] = widget.text()
            elif isinstance(widget, QSpinBox) or isinstance(widget, QDoubleSpinBox):
                params[group][name] = widget.value()
            elif isinstance(widget, QCheckBox):
                params[group][name] = widget.isChecked()
            elif isinstance(widget, QComboBox):
                params[group][name] = widget.currentText()
        
        return params
    
    def save_parameters(self):
        """保存参数到文件"""
        params = self.get_parameter_values()
        
        file_path, _ = QFileDialog.getSaveFileName(self, "保存参数文件", "", "参数文件 (*.json)")
        if not file_path:
            return
            
        try:
            import json
            with open(file_path, 'w') as f:
                json.dump(params, f, indent=4)
            QMessageBox.information(self, "成功", "参数已成功保存")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存参数时出错: {str(e)}")
    
    def load_parameters(self):
        """从文件加载参数"""
        file_path, _ = QFileDialog.getOpenFileName(self, "加载参数文件", "", "参数文件 (*.json)")
        if not file_path:
            return
            
        try:
            import json
            with open(file_path, 'r') as f:
                params = json.load(f)
                
            # 更新界面控件的值
            for group, group_params in params.items():
                for name, value in group_params.items():
                    key = f"{group}.{name}"
                    if key in self.param_widgets:
                        widget = self.param_widgets[key]
                        
                        # 根据控件类型设置值
                        if isinstance(widget, QLineEdit):
                            widget.setText(str(value))
                        elif isinstance(widget, QSpinBox):
                            widget.setValue(int(value))
                        elif isinstance(widget, QDoubleSpinBox):
                            widget.setValue(float(value))
                        elif isinstance(widget, QCheckBox):
                            widget.setChecked(bool(value))
                        elif isinstance(widget, QComboBox):
                            index = widget.findText(str(value))
                            if index >= 0:
                                widget.setCurrentIndex(index)
                                
            QMessageBox.information(self, "成功", "参数已成功加载")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载参数时出错: {str(e)}")
    
    def create_parameter_file(self, exe_dir):
        """创建参数文件"""
        params = self.get_parameter_values()
        
        # 创建参数文件
        param_file = os.path.join(exe_dir, "cfd_params.ini")
        
        try:
            with open(param_file, 'w') as f:
                for group, group_params in params.items():
                    f.write(f"[{group}]\n")
                    for name, value in group_params.items():
                        f.write(f"{name} = {value}\n")
                    f.write("\n")
            
            return param_file
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法创建参数文件: {str(e)}")
            return None
    
    def start_calculation(self):
        # 创建参数文件（在当前目录）
        param_file = self.create_parameter_file(os.getcwd())
        if not param_file:
            return
        
        # 获取进程数
        process_count = self.process_count.value()
        
        # 启动计算进程
        self.process = QProcess()
        self.process.readyReadStandardOutput.connect(self.handle_output)
        self.process.readyReadStandardError.connect(self.handle_error)
        self.process.finished.connect(self.process_finished)
        
        # 构建命令：mpiexec -n <进程数> ocfd.exe
        # 使用列表形式传递命令，避免命令注入和空格问题
        command = ["mpiexec", "-n", str(process_count), "ocfd.exe"]
        
        # 显示将要执行的命令
        command_str = ""
        self.status_label.setText(command_str)
        
        # 启动进程
        self.process.start(command[0], command[1:])
        
        if self.process.state() == QProcess.Running:
            self.status_label.setText(f"{command_str}\n计算中...")
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
        else:
            QMessageBox.critical(self, "错误", "无法启动计算进程")
    
    def stop_calculation(self):
        if self.process and self.process.state() == QProcess.Running:
            self.process.terminate()
            self.process.waitForFinished(3000)  # 等待3秒
            if self.process.state() == QProcess.Running:
                self.process.kill()  # 如果进程还在运行，强制结束
            
            self.status_label.setText(self.status_label.text() + "\n计算已停止")
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
    
    def handle_output(self):
        output = self.process.readAllStandardOutput().data().decode('utf-8', errors='ignore')
        current_text = self.status_label.text()
        # 限制显示的行数，避免文本过长
        lines = current_text.split('\n')
        if len(lines) > 15:
            lines = lines[:2] + lines[-13:]  # 保留前两行和最近的13行输出
        
        updated_text = '\n'.join(lines) + '\n' + output.strip()
        self.status_label.setText(updated_text)
    
    def handle_error(self):
        error = self.process.readAllStandardError().data().decode('utf-8', errors='ignore')
        current_text = self.status_label.text()
        updated_text = current_text + '\n错误: ' + error.strip()
        self.status_label.setText(updated_text)
    
    def process_finished(self, exit_code, exit_status):
        if exit_status == QProcess.NormalExit and exit_code == 0:
            self.status_label.setText(self.status_label.text() + "\n计算完成")
        else:
            self.status_label.setText(self.status_label.text() + f"\n计算异常终止，退出代码: {exit_code}")
        
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CFDCalculator()
    window.show()
    sys.exit(app.exec_())